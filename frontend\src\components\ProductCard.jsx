import { Box, Button, Heading, IconButton, Image, Input, Modal, ModalBody, ModalCloseButton, ModalContent, ModalHeader, ModalOverlay, useColorModeValue } from '@chakra-ui/react'
import { Text } from '@chakra-ui/react'
import { HStack } from '@chakra-ui/react'
import { EditIcon, DeleteIcon } from '@chakra-ui/icons'
import { useProductStore } from '../store/product';
import { useToast } from '@chakra-ui/react';
import { VStack , ModalFooter } from '@chakra-ui/react';
import { useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';
import { useUpdateProduct } from '../store/product';



const ProductCard = ({product}) => {

    const [updatedProduct, setUpdateProduct] = useState(product);


    const handleUpdateProduct = async(pid,updateProduct)=>{
        const {success,message} = await updateProduct(pid,updateProduct);
        if(!success){
            toast({
                title: "Error",
                description: message,
                status: "error",
                isClosable: true,
              });
        }else{
            toast({
                title: "Success",
                description: message,
                status: "success",
                isClosable: true,
              });
        }
    }
        
    const textColor = useColorModeValue("gray.600", "gray.200");
    const bg = useColorModeValue("white", "gray.800")

    const { isOpen, onOpen, onClose } = useDisclosure();

    const {deleteProduct,updateProduct} = useProductStore();
    const toast = useToast();

    const handleDeleteProduct = async(pid)=>{
        const {success,message} = await deleteProduct(pid);
        if(!success){
            toast({
                title: "Error",
                description: message,
                status: "error",
                isClosable: true,
              });
        }else{
            toast({
                title: "Success",
                description: message,
                status: "success",
                isClosable: true,
              });
        }
    }
  return (
    <Box
      w={"full"}
      bg={bg}
      p={6}
      rounded={"lg"}
      shadow={"md"}
      _hover={{
        transform: "scale(1.05)",
        transition: "transform 0.2s ease-in-out",
      }}
    >
      <Image
        src={product.image}
        alt={product.name}
        h={48}
        objectFit={"cover"}
        w={"full"}
      />
      <Box p={4}>
        <Heading as={"h3"} size={"lg"} mb={2}>
          {product.name}
        </Heading>
        <Text fontWeight={"bold"} fontSize={"xl"} color={textColor} mb={2}>
          ${product.price}
        </Text>
        <HStack spacing={4}>
          <IconButton icon={<EditIcon />} onClick={onOpen} colorScheme="blue" />
          <IconButton
            icon={<DeleteIcon />}
            onClick={() => {
              handleDeleteProduct(product._id);
            }}
            colorScheme="red"
          />
        </HStack>
      </Box>
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Update Product</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <Input placeholder="Product Name" name="name" value= {updatedProduct.name}/>
              <Input placeholder="Product Price" type={"number"} name="price"  value={updatedProduct.price}/>
              <Input placeholder="Image URL" name="image" value={updatedProduct.image} />
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={()=>handleUpdateProduct(product._id , updateProduct)} >
              Update
            </Button>
            <Button variant="ghost" onClick={onClose}>Cancel </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
}

export default ProductCard