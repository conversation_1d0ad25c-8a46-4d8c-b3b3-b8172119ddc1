import { Box, Button, Heading, IconButton, Image, useColorModeValue } from '@chakra-ui/react'
import { Text } from '@chakra-ui/react'
import { HStack } from '@chakra-ui/react'
import { EditIcon, DeleteIcon } from '@chakra-ui/icons'
import { useProductStore } from '../store/product';
import { useToast } from '@chakra-ui/react';


const ProductCard = ({product}) => {

    
    const textColor = useColorModeValue("gray.600", "gray.200");
    const bg = useColorModeValue("white", "gray.800")
    
    const {deleteProduct} = useProductStore();
    const toast = useToast();
    const {handleDeleteProduct} = async(pid)=>{
        const {success,message} = await deleteProduct(pid);
        if(!success){
            toast({
                title: "Error",
                description: message,
                status: "error",
                isClosable: true,
              });
        }else{
            toast({
                title: "Success",
                description: message,
                status: "success",
                isClosable: true,
              });
        }
    }
  return <Box 
    w={"full"}
    bg={bg}
    p={6}
    rounded={"lg"}
    shadow={"md"}
    _hover ={{
      transform: "scale(1.05)",
      transition: "transform 0.2s ease-in-out",
    }}
  >
    <Image src={product.image} alt={product.name} h={48} objectFit={"cover"} w={"full"}/>
    <Box p={4}>
        <Heading as={'h3'} size={"lg"} mb={2}>
            {product.name}
        </Heading>
        <Text fontWeight={"bold"} fontSize={"xl"} color={textColor} mb={2}>
            ${product.price}
        </Text>
        <HStack spacing={4}>
            <IconButton icon={<EditIcon/>} colorScheme='blue'/>
            <IconButton icon={<DeleteIcon/>} onClick = {() => {handleDeleteProduct(product._id)}} colorScheme='red'/>
        </HStack>

    </Box>

  </Box>
}

export default ProductCard