import express from 'express';
import dotenv from 'dotenv';
import connectDB from './config/db.js';
import productRoutes from './routes/product.routes.js';

dotenv.config();

const app =express();
const port = 3000;

app.use(express.json());

app.use("/api/products", productRoutes);

app.listen(port, () => {
    connectDB();
    console.log(`Server is running on port ${port}`);
});

//SEw8xQke9qicdmaS