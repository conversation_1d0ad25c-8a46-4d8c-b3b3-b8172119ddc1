import {create} from "zustand";

export const useProductStore = create((set)=>({
    product:[],
    setProduct:(product) =>set({product}),
    createProduct: async(newProduct)=>{
        if(!newProduct.name||!newProduct.price||!newProduct.image){
            return{success: false, message: "Please fill all the fields"};
        }
        const res =await fetch("/api/products",{
            method:"POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(newProduct), 
        })
        const data = await res.json();
        set((state)=>({product: [...state.product, data.newProduct]}));
        return{success: true, message: "Product created"};

        
    },
    fetchProducts: async()=>{
        const res = await fetch("/api/products");
        const data = await res.json();
        set({product: data});
    },
    deleteProduct: async(pid)=>{
      const res = await fetch(`/api/products/${pid}`, {
        method: "DELETE",
      });
      const data = await res.json();
      if (!data.success) {
        return { success: false, message: data.message };
      }
      //update the ui immediately without needing to refetch the products
      set((state) => ({ product: state.product.filter((product) => product._id !== pid),}));
      return { success: true, message: "Product deleted" };
    },
    updateProduct:async(pid,updateProduct)=>{
      const res = await fetch(`/api/products/${pid}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateProduct),
      });
      const data = await res.json();
      if (!data.success) {
        return { success: false, message: data.message };
      }
      //update the ui immediately without needing to refetch the products
      set((state) => ({ product: state.product.map(product => product._i === pid ?  updateProduct : data.updatedProduct),}));
      return { success: true, message: "Product updated" };
    },    
}));

